package com.xinghuo.project.ticket.model;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 项目实际风险实例视图对象
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@Schema(description = "项目实际风险实例视图对象")
public class ProjectInsRiskVO {
    
    @Schema(description = "风险实例ID")
    private String id;
    
    @Schema(description = "所属的实际项目ID")
    private String projectId;
    
    @Schema(description = "项目名称")
    private String projectName;
    
    @Schema(description = "源自哪个标准风险库ID")
    private String sourceLibraryRiskId;
    
    @Schema(description = "源风险标题")
    private String sourceRiskTitle;
    
    @Schema(description = "风险标题")
    private String title;
    
    @Schema(description = "风险描述")
    private String description;
    
    @Schema(description = "风险分类ID")
    private String riskCategoryId;
    
    @Schema(description = "风险分类名称")
    private String riskCategoryName;
    
    @Schema(description = "概率等级ID")
    private String probabilityLevelId;
    
    @Schema(description = "概率等级名称")
    private String probabilityLevelName;
    
    @Schema(description = "影响等级ID")
    private String impactLevelId;
    
    @Schema(description = "影响等级名称")
    private String impactLevelName;
    
    @Schema(description = "风险评分")
    private Integer riskScore;
    
    @Schema(description = "应对策略")
    private String strategy;
    
    @Schema(description = "应对措施")
    private String actions;
    
    @Schema(description = "负责人用户ID")
    private String responseUserId;
    
    @Schema(description = "负责人姓名")
    private String responseUserName;
    
    @Schema(description = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dueDate;
    
    @Schema(description = "状态ID")
    private String statusId;
    
    @Schema(description = "状态名称")
    private String statusName;
    
    @Schema(description = "日志记录")
    private String log;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;
    
    @Schema(description = "创建人")
    private String createdBy;
    
    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdatedAt;
    
    @Schema(description = "最后更新人")
    private String lastUpdatedBy;
}