package com.xinghuo.project.ticket.model;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * 项目实际问题实例表单对象
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@Schema(description = "项目实际问题实例表单对象")
public class ProjectInsIssueForm {
    
    @Schema(description = "问题实例ID，新增时为空，更新时必填")
    private String id;
    
    @Schema(description = "所属的实际项目ID")
    @NotBlank(message = "项目ID不能为空")
    private String projectId;
    
    @Schema(description = "源自哪个标准问题库ID")
    private String sourceLibraryIssueId;
    
    @Schema(description = "问题标题")
    @NotBlank(message = "问题标题不能为空")
    private String title;
    
    @Schema(description = "问题描述")
    private String description;
    
    @Schema(description = "问题分类ID")
    @NotBlank(message = "问题分类不能为空")
    private String issueCategoryId;
    
    @Schema(description = "优先级ID")
    private String priorityId;
    
    @Schema(description = "解决者用户ID")
    private String solverUserId;
    
    @Schema(description = "截止日期")
    private Date dueDate;
    
    @Schema(description = "解决方案")
    private String solution;
    
    @Schema(description = "状态ID")
    @NotBlank(message = "状态不能为空")
    private String statusId;
    
    @Schema(description = "日志记录")
    private String log;
    
    @Schema(description = "关联任务ID")
    private String relatedTaskId;
}