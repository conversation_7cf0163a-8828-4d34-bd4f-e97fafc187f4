package com.xinghuo.project.ticket.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import com.xinghuo.common.base.model.Pagination;
import java.util.Date;

/**
 * 项目实际问题实例分页查询
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "项目实际问题实例分页查询")
public class ProjectInsIssuePagination extends Pagination {
    
    @Schema(description = "所属的实际项目ID")
    private String projectId;
    
    @Schema(description = "问题标题")
    private String title;
    
    @Schema(description = "问题分类ID")
    private String issueCategoryId;
    
    @Schema(description = "优先级ID")
    private String priorityId;
    
    @Schema(description = "解决者用户ID")
    private String solverUserId;
    
    @Schema(description = "状态ID")
    private String statusId;
    
    @Schema(description = "创建开始时间")
    private Date startTime;
    
    @Schema(description = "创建结束时间")
    private Date endTime;
    
    @Schema(description = "截止日期开始")
    private Date dueDateStart;
    
    @Schema(description = "截止日期结束")
    private Date dueDateEnd;
}