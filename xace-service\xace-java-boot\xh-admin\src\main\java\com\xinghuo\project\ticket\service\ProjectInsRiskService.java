package com.xinghuo.project.ticket.service;

import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.project.ticket.model.ProjectInsRiskForm;
import com.xinghuo.project.ticket.model.ProjectInsRiskPagination;
import com.xinghuo.project.ticket.model.ProjectInsRiskVO;

import java.util.List;

/**
 * 项目实际风险实例服务接口
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface ProjectInsRiskService {
    
    /**
     * 根据ID获取风险实例信息
     */
    ProjectInsRiskVO getInfo(String id);
    
    /**
     * 分页查询风险实例列表
     */
    PageListVO<ProjectInsRiskVO> getList(ProjectInsRiskPagination pagination);
    
    /**
     * 根据项目ID获取风险列表
     */
    List<ProjectInsRiskVO> getListByProjectId(String projectId);
    
    /**
     * 创建风险实例
     */
    String create(ProjectInsRiskForm form);
    
    /**
     * 更新风险实例
     */
    boolean update(ProjectInsRiskForm form);
    
    /**
     * 删除风险实例
     */
    boolean delete(String id);
    
    /**
     * 批量删除风险实例
     */
    boolean deleteBatch(List<String> ids);
    
    /**
     * 从模板库复制风险到项目
     */
    String copyFromTemplate(String projectId, String sourceLibraryRiskId);
    
    /**
     * 计算风险评分
     */
    Integer calculateRiskScore(String probabilityLevelId, String impactLevelId);
}