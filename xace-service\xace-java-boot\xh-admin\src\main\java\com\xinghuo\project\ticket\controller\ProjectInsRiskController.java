package com.xinghuo.project.ticket.controller;

import lombok.extern.slf4j.Slf4j;
import com.xinghuo.project.ticket.model.ProjectInsRiskForm;
import com.xinghuo.project.ticket.model.ProjectInsRiskPagination;
import com.xinghuo.project.ticket.model.ProjectInsRiskVO;
import com.xinghuo.project.ticket.service.ProjectInsRiskService;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;

import java.util.List;

/**
 * 项目实际风险实例控制器
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Tag(name = "项目实际风险实例管理", description = "项目实际风险实例管理相关接口")
@RestController
@RequestMapping("/api/project/ticket/risk")
public class ProjectInsRiskController {

    @Resource
    private ProjectInsRiskService projectInsRiskService;
    
    @Operation(summary = "获取风险实例详情")
    @GetMapping("/{id}")
    public ActionResult<ProjectInsRiskVO> getInfo(@Parameter(description = "风险实例ID") @PathVariable String id) {
        ProjectInsRiskVO info = projectInsRiskService.getInfo(id);
        return ActionResult.success(info);
    }
    
    @Operation(summary = "分页查询风险实例列表")
    @PostMapping("/getList")
    public ActionResult<PageListVO<ProjectInsRiskVO>> getList(@RequestBody ProjectInsRiskPagination pagination) {
        PageListVO<ProjectInsRiskVO> result = projectInsRiskService.getList(pagination);
        return ActionResult.success(result);
    }
    
    @Operation(summary = "根据项目ID获取风险列表")
    @GetMapping("/project/{projectId}")
    public ActionResult<List<ProjectInsRiskVO>> getListByProjectId(@Parameter(description = "项目ID") @PathVariable String projectId) {
        List<ProjectInsRiskVO> list = projectInsRiskService.getListByProjectId(projectId);
        return ActionResult.success(list);
    }
    
    @Operation(summary = "创建风险实例")
    @PostMapping
    public ActionResult<String> create(@Valid @RequestBody ProjectInsRiskForm form) {
        String id = projectInsRiskService.create(form);
        return ActionResult.success(id);
    }
    
    @Operation(summary = "更新风险实例")
    @PutMapping("/{id}")
    public ActionResult<Boolean> update(@Parameter(description = "风险实例ID") @PathVariable String id, @Valid @RequestBody ProjectInsRiskForm form) {
        form.setId(id);
        boolean result = projectInsRiskService.update(form);
        return ActionResult.success(result);
    }
    
    @Operation(summary = "删除风险实例")
    @DeleteMapping("/{id}")
    public ActionResult<Boolean> delete(@Parameter(description = "风险实例ID") @PathVariable String id) {
        boolean result = projectInsRiskService.delete(id);
        return ActionResult.success(result);
    }
    
    @Operation(summary = "批量删除风险实例")
    @DeleteMapping("/batch")
    public ActionResult<Boolean> deleteBatch(@RequestBody List<String> ids) {
        boolean result = projectInsRiskService.deleteBatch(ids);
        return ActionResult.success(result);
    }
    
    @Operation(summary = "从模板库复制风险到项目")
    @PostMapping("/copy-from-template")
    public ActionResult<String> copyFromTemplate(@Parameter(description = "项目ID") @RequestParam String projectId, 
                                                 @Parameter(description = "源风险库ID") @RequestParam String sourceLibraryRiskId) {
        String id = projectInsRiskService.copyFromTemplate(projectId, sourceLibraryRiskId);
        return ActionResult.success(id);
    }
    
    @Operation(summary = "计算风险评分")
    @GetMapping("/calculate-score")
    public ActionResult<Integer> calculateRiskScore(@Parameter(description = "概率等级ID") @RequestParam String probabilityLevelId,
                                                   @Parameter(description = "影响等级ID") @RequestParam String impactLevelId) {
        Integer riskScore = projectInsRiskService.calculateRiskScore(probabilityLevelId, impactLevelId);
        return ActionResult.success(riskScore);
    }
}