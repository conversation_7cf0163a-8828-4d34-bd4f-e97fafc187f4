package com.xinghuo.project.ticket.service;

import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.project.ticket.model.ProjectInsIssueForm;
import com.xinghuo.project.ticket.model.ProjectInsIssuePagination;
import com.xinghuo.project.ticket.model.ProjectInsIssueVO;
import java.util.List;

/**
 * 项目实际问题实例服务接口
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface ProjectInsIssueService {
    
    /**
     * 根据ID获取问题实例信息
     */
    ProjectInsIssueVO getInfo(String id);
    
    /**
     * 分页查询问题实例列表
     */
    PageListVO<ProjectInsIssueVO> getList(ProjectInsIssuePagination pagination);
    
    /**
     * 根据项目ID获取问题列表
     */
    List<ProjectInsIssueVO> getListByProjectId(String projectId);
    
    /**
     * 创建问题实例
     */
    String create(ProjectInsIssueForm form);
    
    /**
     * 更新问题实例
     */
    boolean update(ProjectInsIssueForm form);
    
    /**
     * 删除问题实例
     */
    boolean delete(String id);
    
    /**
     * 批量删除问题实例
     */
    boolean deleteBatch(List<String> ids);
    
    /**
     * 从模板库复制问题到项目
     */
    String copyFromTemplate(String projectId, String sourceLibraryIssueId);
}