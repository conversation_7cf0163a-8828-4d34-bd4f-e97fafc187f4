package com.xinghuo.project.ticket.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.project.ticket.dao.ProjectInsIssueMapper;
import com.xinghuo.project.ticket.entity.ProjectInsIssueEntity;
import com.xinghuo.project.ticket.model.ProjectInsIssueForm;
import com.xinghuo.project.ticket.model.ProjectInsIssuePagination;
import com.xinghuo.project.ticket.model.ProjectInsIssueVO;
import com.xinghuo.project.ticket.service.ProjectInsIssueService;
import java.util.List;

/**
 * 项目实际问题实例服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
@Transactional
public class ProjectInsIssueServiceImpl extends ServiceImpl<ProjectInsIssueMapper, ProjectInsIssueEntity> implements ProjectInsIssueService {
    
    @Override
    public ProjectInsIssueVO getInfo(String id) {
        ProjectInsIssueEntity entity = this.getById(id);
        if (entity == null) {
            return null;
        }
        
        ProjectInsIssueVO vo = BeanCopierUtils.copy(entity, ProjectInsIssueVO.class);
        // TODO: 设置关联对象的名称字段，如项目名称、分类名称、状态名称等
        return vo;
    }
    
    @Override
    public PageListVO<ProjectInsIssueVO> getList(ProjectInsIssuePagination pagination) {
        LambdaQueryWrapper<ProjectInsIssueEntity> lambda = new LambdaQueryWrapper<>();
        
        // 查询条件
        if (StrXhUtil.isNotEmpty(pagination.getProjectId())) {
            lambda.eq(ProjectInsIssueEntity::getProjectId, pagination.getProjectId());
        }
        if (StrXhUtil.isNotEmpty(pagination.getTitle())) {
            lambda.like(ProjectInsIssueEntity::getTitle, pagination.getTitle());
        }
        if (StrXhUtil.isNotEmpty(pagination.getIssueCategoryId())) {
            lambda.eq(ProjectInsIssueEntity::getIssueCategoryId, pagination.getIssueCategoryId());
        }
        if (StrXhUtil.isNotEmpty(pagination.getPriorityId())) {
            lambda.eq(ProjectInsIssueEntity::getPriorityId, pagination.getPriorityId());
        }
        if (StrXhUtil.isNotEmpty(pagination.getSolverUserId())) {
            lambda.eq(ProjectInsIssueEntity::getSolverUserId, pagination.getSolverUserId());
        }
        if (StrXhUtil.isNotEmpty(pagination.getStatusId())) {
            lambda.eq(ProjectInsIssueEntity::getStatusId, pagination.getStatusId());
        }
        if (pagination.getStartTime() != null) {
            lambda.ge(ProjectInsIssueEntity::getCreatedAt, pagination.getStartTime());
        }
        if (pagination.getEndTime() != null) {
            lambda.le(ProjectInsIssueEntity::getCreatedAt, pagination.getEndTime());
        }
        if (pagination.getDueDateStart() != null) {
            lambda.ge(ProjectInsIssueEntity::getDueDate, pagination.getDueDateStart());
        }
        if (pagination.getDueDateEnd() != null) {
            lambda.le(ProjectInsIssueEntity::getDueDate, pagination.getDueDateEnd());
        }
        
        // 排序
        lambda.orderByDesc(ProjectInsIssueEntity::getCreatedAt);
        
        // 分页查询
        IPage<ProjectInsIssueEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<ProjectInsIssueEntity> result = this.page(page, lambda);
        
        // 转换为VO
        List<ProjectInsIssueVO> list = BeanCopierUtils.copyList(result.getRecords(), ProjectInsIssueVO.class);
        // TODO: 设置关联对象的名称字段
        
        PaginationVO paginationVO = new PaginationVO();
        paginationVO.setCurrentPage(pagination.getCurrentPage());
        paginationVO.setPageSize(pagination.getPageSize());
        paginationVO.setTotal(result.getTotal());
        
        return new PageListVO<>(list, paginationVO);
    }
    
    @Override
    public List<ProjectInsIssueVO> getListByProjectId(String projectId) {
        LambdaQueryWrapper<ProjectInsIssueEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectInsIssueEntity::getProjectId, projectId);
        lambda.orderByDesc(ProjectInsIssueEntity::getCreatedAt);
        
        List<ProjectInsIssueEntity> entities = this.list(lambda);
        List<ProjectInsIssueVO> list = BeanCopierUtils.copyList(entities, ProjectInsIssueVO.class);
        // TODO: 设置关联对象的名称字段
        
        return list;
    }
    
    @Override
    public String create(ProjectInsIssueForm form) {
        ProjectInsIssueEntity entity = BeanCopierUtils.copy(form, ProjectInsIssueEntity.class);
        entity.setId(RandomUtil.snowId());
        
        this.save(entity);
        return entity.getId();
    }
    
    @Override
    public boolean update(ProjectInsIssueForm form) {
        ProjectInsIssueEntity entity = BeanCopierUtils.copy(form, ProjectInsIssueEntity.class);
        return this.updateById(entity);
    }
    
    @Override
    public boolean delete(String id) {
        return this.removeById(id);
    }
    
    @Override
    public boolean deleteBatch(List<String> ids) {
        return this.removeByIds(ids);
    }
    
    @Override
    public String copyFromTemplate(String projectId, String sourceLibraryIssueId) {
        // TODO: 实现从模板库复制问题逻辑
        // 1. 从问题库获取模板问题
        // 2. 复制到项目实例
        // 3. 返回新创建的实例ID
        return null;
    }
}